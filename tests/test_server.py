import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from datetime import datetime, timezone, timedelta
import sys

# Mock fastmcp before importing server module
sys.modules["fastmcp"] = Mock()

from src.models import (
    CalcomBooking,
    CreateBookingRequest,
    EventType,
    BookingListResponse,
    EventTypeListResponse,
    CreateBookingResponse,
    AvailabilityRequest,
    AvailabilityResponse,
    AvailabilitySlot,
)


# Mock the FastMCP and Context classes
class MockContext:
    async def info(self, message):
        pass

    async def error(self, message):
        pass

    async def warning(self, message):
        pass


class MockFastMCP:
    def __init__(self, name):
        self.name = name

    def tool(self, func):
        return func

    def resource(self, uri):
        def decorator(func):
            return func

        return decorator

    def custom_route(self, path, methods=None):
        def decorator(func):
            return func

        return decorator


# Patch fastmcp imports
with patch.dict(
    "sys.modules", {"fastmcp": Mock(FastMCP=MockFastMCP, Context=MockContext)}
):
    # Import the actual function implementations directly
    from src.server import (
        list_bookings,
        create_booking,
        list_event_types,
        check_availability,
    )


class TestSimplifiedTools:
    """Test the simplified tool interfaces"""

    @pytest.mark.asyncio
    async def test_list_bookings_basic(self):
        """Test list_bookings function basic structure"""
        ctx = MockContext()

        # Mock the CalcomClient to avoid actual API calls
        with patch("src.server.CalcomClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance

            # Mock empty response
            mock_response = Mock()
            mock_response.bookings = []
            mock_client_instance.get_bookings.return_value = mock_response

            result = await list_bookings(ctx)

            # Should return a string
            assert isinstance(result, str)
            assert "No bookings found" in result

    def test_create_booking_model_validation(self):
        """Test that CreateBookingRequest model doesn't have end_time field"""
        from src.models import CreateBookingRequest

        # Test that we can create a request without end_time
        request = CreateBookingRequest(
            event_type_id=123,
            start_time="2024-01-15T09:00:00",
            attendee_name="John Doe",
            attendee_email="<EMAIL>",
        )

        # Verify the fields
        assert request.event_type_id == 123
        assert request.start_time == "2024-01-15T09:00:00"
        assert request.attendee_name == "John Doe"
        assert request.attendee_email == "<EMAIL>"

        # Verify end_time is not in the model
        assert not hasattr(
            request, "end_time"
        ), "end_time field should be removed from CreateBookingRequest"

    @pytest.mark.asyncio
    async def test_list_event_types_basic(self):
        """Test list_event_types function basic structure"""
        ctx = MockContext()

        # Mock the CalcomClient to avoid actual API calls
        with patch("src.server.CalcomClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance

            # Mock response with event types
            mock_event_type = Mock()
            mock_event_type.id = 123
            mock_event_type.title = "Test Meeting"
            mock_event_type.length = 30

            mock_response = Mock()
            mock_response.event_types = [mock_event_type]
            mock_client_instance.get_event_types.return_value = mock_response

            result = await list_event_types(ctx)

            # Should return a string
            assert isinstance(result, str)
            assert "Found 1 event types" in result
            assert "ID: 123" in result
            assert "Test Meeting" in result

    @pytest.mark.asyncio
    async def test_check_availability_basic(self):
        """Test check_availability function basic structure"""
        ctx = MockContext()

        # Mock the CalcomClient to avoid actual API calls
        with patch("src.server.CalcomClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client.return_value.__aenter__.return_value = mock_client_instance

            # Mock response with no available slots
            mock_response = Mock()
            mock_response.success = True
            mock_response.total_slots = 0
            mock_response.available_slots = []
            mock_client_instance.check_availability.return_value = mock_response

            result = await check_availability(
                event_type_id=123, date="2024-01-15", ctx=ctx
            )

            # Should return a string
            assert isinstance(result, str)
            assert "No available time slots found" in result


if __name__ == "__main__":
    pytest.main([__file__])

import pytest
import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch, Mock
from datetime import datetime, timezone, timedelta
import sys

# Mock fastmcp before importing server module
sys.modules["fastmcp"] = Mock()

from src.models import (
    CalcomBooking,
    CreateBookingRequest,
    EventType,
    BookingListResponse,
    EventTypeListResponse,
    CreateBookingResponse,
    AvailabilityRequest,
    AvailabilityResponse,
    AvailabilitySlot,
)


# Mock the FastMCP and Context classes
class MockContext:
    async def info(self, message):
        pass

    async def error(self, message):
        pass

    async def warning(self, message):
        pass


class MockFastMCP:
    def __init__(self, name):
        self.name = name

    def tool(self, func):
        return func

    def resource(self, uri):
        def decorator(func):
            return func

        return decorator

    def custom_route(self, path, methods=None):
        def decorator(func):
            return func

        return decorator


# Patch fastmcp imports
with patch.dict(
    "sys.modules", {"fastmcp": Mock(FastMCP=MockFastMCP, Context=MockContext)}
):
    # Import the actual function implementations directly
    from src.server import (
        fetch_calcom_bookings,
        create_calcom_booking,
        get_calcom_event_types,
        check_calcom_availability,
    )


class TestBasicFunctionality:
    """Test basic functionality that doesn't require complex mocking"""

    @pytest.mark.asyncio
    async def test_check_availability_date_validation(self):
        """Test date format validation"""
        result = await check_calcom_availability(
            event_type_id=123,
            date_from="invalid-date",
        )

        assert result["structuredContent"]["success"] is False
        assert result["structuredContent"]["error_type"] == "validation_error"
        assert result["isError"] is True
        assert "YYYY-MM-DD format" in result["structuredContent"]["error"]

    @pytest.mark.asyncio
    async def test_create_booking_validation_basic(self):
        """Test basic validation without complex mocking"""
        # Test with invalid email format - this should trigger Pydantic validation
        result = await create_calcom_booking(
            event_type_id=456,
            start_time="2024-01-15T09:00:00",
            end_time="2024-01-15T09:30:00",
            attendee_name="John Doe",
            attendee_email="invalid-email",  # Invalid email format
        )

        # Should handle validation error
        assert result["structuredContent"]["success"] is False
        assert result["isError"] is True


if __name__ == "__main__":
    pytest.main([__file__])

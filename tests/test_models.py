import pytest
from datetime import datetime, timezone
from pydantic import ValidationError

from src.models import (
    CalcomBooking,
    CreateBookingRequest,
    EventType,
    BookingListResponse,
    EventTypeListResponse,
    CreateBookingResponse,
    AvailabilitySlot,
    AvailabilityRequest,
    AvailabilityResponse,
)


class TestCalcomBooking:
    """Test cases for CalcomBooking model"""

    def test_calcom_booking_creation(self):
        """Test creating a CalcomBooking instance"""
        now = datetime.now(timezone.utc)
        booking = CalcomBooking(
            id=123,
            title="Test Meeting",
            description="A test meeting",
            start_time=now,
            end_time=now,
            attendees=[{"name": "<PERSON>", "email": "<EMAIL>"}],
            location="Zoom",
            event_type_id=456,
            status="confirmed",
            uid="test-uid-123",
        )

        assert booking.id == 123
        assert booking.title == "Test Meeting"
        assert booking.description == "A test meeting"
        assert booking.start_time == now
        assert booking.end_time == now
        assert len(booking.attendees) == 1
        assert booking.location == "Zoom"
        assert booking.event_type_id == 456
        assert booking.status == "confirmed"
        assert booking.uid == "test-uid-123"

    def test_calcom_booking_defaults(self):
        """Test CalcomBooking with default values"""
        now = datetime.now(timezone.utc)
        booking = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=now,
            end_time=now,
            event_type_id=456,
            uid="test-uid-123",
        )

        assert booking.description is None
        assert booking.attendees == []
        assert booking.location is None
        assert booking.status == "confirmed"

    def test_calcom_booking_validation(self):
        """Test CalcomBooking validation"""
        now = datetime.now(timezone.utc)

        # Test missing required fields
        with pytest.raises(ValidationError):
            CalcomBooking()  # Missing all required fields

        with pytest.raises(ValidationError):
            CalcomBooking(
                id=123,
                title="Test Meeting",
                # Missing start_time, end_time, event_type_id, uid
            )

    def test_calcom_booking_serialization(self):
        """Test CalcomBooking serialization"""
        now = datetime.now(timezone.utc)
        booking = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=now,
            end_time=now,
            event_type_id=456,
            uid="test-uid-123",
        )

        data = booking.model_dump()
        assert isinstance(data, dict)
        assert data["id"] == 123
        assert data["title"] == "Test Meeting"
        assert "start_time" in data
        assert "end_time" in data


class TestCreateBookingRequest:
    """Test cases for CreateBookingRequest model"""

    def test_create_booking_request_creation(self):
        """Test creating a CreateBookingRequest instance"""
        request = CreateBookingRequest(
            event_type_id=456,
            start="2024-01-15T14:00:00",
            end="2024-01-15T15:00:00",
            attendee_name="John Doe",
            attendee_email="<EMAIL>",
            attendee_timezone="America/New_York",
            location="Conference Room A",
            notes="Important meeting",
            language="en",
        )

        assert request.event_type_id == 456
        assert request.start == "2024-01-15T14:00:00"
        assert request.end == "2024-01-15T15:00:00"
        assert request.attendee_name == "John Doe"
        assert request.attendee_email == "<EMAIL>"
        assert request.attendee_timezone == "America/New_York"
        assert request.location == "Conference Room A"
        assert request.notes == "Important meeting"
        assert request.language == "en"

    def test_create_booking_request_defaults(self):
        """Test CreateBookingRequest with default values"""
        request = CreateBookingRequest(
            event_type_id=456,
            start="2024-01-15T14:00:00",
            end="2024-01-15T15:00:00",
            attendee_name="John Doe",
            attendee_email="<EMAIL>",
        )

        assert request.attendee_timezone == "UTC"
        assert request.location is None
        assert request.notes is None
        assert request.language == "en"

    def test_create_booking_request_validation(self):
        """Test CreateBookingRequest validation"""
        # Test missing required fields
        with pytest.raises(ValidationError):
            CreateBookingRequest()

        with pytest.raises(ValidationError):
            CreateBookingRequest(
                event_type_id=456,
                start="2024-01-15T14:00:00",
                # Missing end, attendee_name, attendee_email
            )

    def test_create_booking_request_field_descriptions(self):
        """Test that field descriptions are properly set"""
        fields = CreateBookingRequest.model_fields

        assert fields["event_type_id"].description == "Cal.com Event Type ID"
        assert fields["start"].description == "Start time in ISO format"
        assert fields["end"].description == "End time in ISO format"
        assert fields["attendee_name"].description == "Attendee name"
        assert fields["attendee_email"].description == "Attendee email"


class TestEventType:
    """Test cases for EventType model"""

    def test_event_type_creation(self):
        """Test creating an EventType instance"""
        event_type = EventType(
            id=123,
            title="30 Min Meeting",
            slug="30min",
            length=30,
            description="Quick meeting",
            locations=[{"type": "zoom", "url": "https://zoom.us"}],
        )

        assert event_type.id == 123
        assert event_type.title == "30 Min Meeting"
        assert event_type.slug == "30min"
        assert event_type.length == 30
        assert event_type.description == "Quick meeting"
        assert len(event_type.locations) == 1

    def test_event_type_defaults(self):
        """Test EventType with default values"""
        event_type = EventType(
            id=123,
            title="30 Min Meeting",
            slug="30min",
            length=30,
        )

        assert event_type.description is None
        assert event_type.locations == []

    def test_event_type_validation(self):
        """Test EventType validation"""
        # Test missing required fields
        with pytest.raises(ValidationError):
            EventType()

        with pytest.raises(ValidationError):
            EventType(
                id=123,
                title="30 Min Meeting",
                # Missing slug and length
            )


class TestBookingListResponse:
    """Test cases for BookingListResponse model"""

    def test_booking_list_response_creation(self):
        """Test creating a BookingListResponse instance"""
        now = datetime.now(timezone.utc)
        booking = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=now,
            end_time=now,
            event_type_id=456,
            uid="test-uid-123",
        )

        response = BookingListResponse(
            bookings=[booking],
            total_count=1,
            message="Success",
        )

        assert len(response.bookings) == 1
        assert response.total_count == 1
        assert response.message == "Success"
        assert isinstance(response.bookings[0], CalcomBooking)

    def test_booking_list_response_empty(self):
        """Test BookingListResponse with empty bookings"""
        response = BookingListResponse(
            bookings=[],
            total_count=0,
            message="No bookings found",
        )

        assert len(response.bookings) == 0
        assert response.total_count == 0


class TestEventTypeListResponse:
    """Test cases for EventTypeListResponse model"""

    def test_event_type_list_response_creation(self):
        """Test creating an EventTypeListResponse instance"""
        event_type = EventType(
            id=123,
            title="30 Min Meeting",
            slug="30min",
            length=30,
        )

        response = EventTypeListResponse(
            event_types=[event_type],
            total_count=1,
            message="Success",
        )

        assert len(response.event_types) == 1
        assert response.total_count == 1
        assert response.message == "Success"
        assert isinstance(response.event_types[0], EventType)


class TestCreateBookingResponse:
    """Test cases for CreateBookingResponse model"""

    def test_create_booking_response_success(self):
        """Test creating a successful CreateBookingResponse"""
        now = datetime.now(timezone.utc)
        booking = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=now,
            end_time=now,
            event_type_id=456,
            uid="test-uid-123",
        )

        response = CreateBookingResponse(
            success=True,
            booking_id=123,
            booking_uid="test-uid-123",
            booking_url="https://zoom.us/j/123456789",
            message="Booking created successfully",
            booking=booking,
        )

        assert response.success is True
        assert response.booking_id == 123
        assert response.booking_uid == "test-uid-123"
        assert response.booking_url == "https://zoom.us/j/123456789"
        assert response.message == "Booking created successfully"
        assert response.booking is not None
        assert response.error_code is None
        assert response.error_details is None

    def test_create_booking_response_failure(self):
        """Test creating a failed CreateBookingResponse"""
        response = CreateBookingResponse(
            success=False,
            message="Booking creation failed",
            error_code="no_available_users_found_error",
            error_details={"reason": "No available time slots"},
        )

        assert response.success is False
        assert response.booking_id is None
        assert response.booking_uid is None
        assert response.booking_url is None
        assert response.message == "Booking creation failed"
        assert response.booking is None
        assert response.error_code == "no_available_users_found_error"
        assert response.error_details == {"reason": "No available time slots"}

    def test_create_booking_response_defaults(self):
        """Test CreateBookingResponse with default values"""
        response = CreateBookingResponse(
            success=False,
            message="Error",
        )

        assert response.booking_id is None
        assert response.booking_uid is None
        assert response.booking_url is None
        assert response.booking is None
        assert response.error_code is None
        assert response.error_details is None


class TestAvailabilitySlot:
    """Test cases for AvailabilitySlot model"""

    def test_availability_slot_creation(self):
        """Test creating an AvailabilitySlot instance"""
        slot = AvailabilitySlot(
            time="2024-01-15T09:00:00Z - 2024-01-15T09:30:00Z",
            attendees=2,
            booking_url="https://cal.com/book/test-event",
        )

        assert slot.time == "2024-01-15T09:00:00Z - 2024-01-15T09:30:00Z"
        assert slot.attendees == 2
        assert slot.booking_url == "https://cal.com/book/test-event"

    def test_availability_slot_defaults(self):
        """Test AvailabilitySlot with default values"""
        slot = AvailabilitySlot(
            time="2024-01-15T09:00:00Z - 2024-01-15T09:30:00Z",
        )

        assert slot.attendees == 0
        assert slot.booking_url is None

    def test_availability_slot_field_descriptions(self):
        """Test that field descriptions are properly set"""
        fields = AvailabilitySlot.model_fields

        assert "Available time range" in fields["time"].description
        assert fields["attendees"].description == "Number of attendees for this slot"
        assert "Direct booking URL" in fields["booking_url"].description


class TestAvailabilityRequest:
    """Test cases for AvailabilityRequest model"""

    def test_availability_request_creation(self):
        """Test creating an AvailabilityRequest instance"""
        request = AvailabilityRequest(
            event_type_id=456,
            date_from="2024-01-15",
            date_to="2024-01-16",
            timezone="America/New_York",
            username="testuser",
            user_id=123,
            team_id=789,
        )

        assert request.event_type_id == 456
        assert request.date_from == "2024-01-15"
        assert request.date_to == "2024-01-16"
        assert request.timezone == "America/New_York"
        assert request.username == "testuser"
        assert request.user_id == 123
        assert request.team_id == 789

    def test_availability_request_defaults(self):
        """Test AvailabilityRequest with default values"""
        request = AvailabilityRequest(
            event_type_id=456,
            date_from="2024-01-15",
            date_to="2024-01-16",
        )

        assert request.timezone == "UTC"
        assert request.username is None
        assert request.user_id is None
        assert request.team_id is None

    def test_availability_request_validation(self):
        """Test AvailabilityRequest validation"""
        # Test missing required fields
        with pytest.raises(ValidationError):
            AvailabilityRequest()

        with pytest.raises(ValidationError):
            AvailabilityRequest(
                event_type_id=456,
                # Missing date_from and date_to
            )


class TestAvailabilityResponse:
    """Test cases for AvailabilityResponse model"""

    def test_availability_response_creation(self):
        """Test creating an AvailabilityResponse instance"""
        slots = [
            AvailabilitySlot(
                time="2024-01-15T09:00:00Z - 2024-01-15T09:30:00Z",
                attendees=0,
            ),
            AvailabilitySlot(
                time="2024-01-15T10:00:00Z - 2024-01-15T10:30:00Z",
                attendees=1,
            ),
        ]

        response = AvailabilityResponse(
            success=True,
            event_type_id=456,
            date="2024-01-15",
            available_slots=slots,
            timezone="America/New_York",
            message="Found 2 available slots",
            total_slots=2,
        )

        assert response.success is True
        assert response.event_type_id == 456
        assert response.date == "2024-01-15"
        assert len(response.available_slots) == 2
        assert response.timezone == "America/New_York"
        assert response.message == "Found 2 available slots"
        assert response.total_slots == 2

    def test_availability_response_defaults(self):
        """Test AvailabilityResponse with default values"""
        response = AvailabilityResponse(
            success=False,
            event_type_id=456,
            date="2024-01-15",
            message="No slots available",
        )

        assert response.available_slots == []
        assert response.timezone == "UTC"
        assert response.total_slots == 0

    def test_availability_response_field_descriptions(self):
        """Test that field descriptions are properly set"""
        fields = AvailabilityResponse.model_fields

        assert "Date checked in YYYY-MM-DD format" in fields["date"].description
        assert "Timezone for the slots" in fields["timezone"].description
        assert "Total number of available slots" in fields["total_slots"].description


class TestModelSerialization:
    """Test cases for model serialization and deserialization"""

    def test_calcom_booking_round_trip(self):
        """Test CalcomBooking serialization and deserialization"""
        now = datetime.now(timezone.utc)
        original = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=now,
            end_time=now,
            event_type_id=456,
            uid="test-uid-123",
        )

        # Serialize to dict
        data = original.model_dump()

        # Deserialize back to object
        restored = CalcomBooking.model_validate(data)

        assert restored.id == original.id
        assert restored.title == original.title
        assert restored.uid == original.uid

    def test_create_booking_request_round_trip(self):
        """Test CreateBookingRequest serialization and deserialization"""
        original = CreateBookingRequest(
            event_type_id=456,
            start="2024-01-15T14:00:00",
            end="2024-01-15T15:00:00",
            attendee_name="John Doe",
            attendee_email="<EMAIL>",
        )

        # Serialize to dict
        data = original.model_dump()

        # Deserialize back to object
        restored = CreateBookingRequest.model_validate(data)

        assert restored.event_type_id == original.event_type_id
        assert restored.attendee_name == original.attendee_name
        assert restored.attendee_email == original.attendee_email

    def test_json_serialization(self):
        """Test JSON serialization of models"""
        now = datetime.now(timezone.utc)
        booking = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=now,
            end_time=now,
            event_type_id=456,
            uid="test-uid-123",
        )

        # Test JSON serialization
        json_str = booking.model_dump_json()
        assert isinstance(json_str, str)
        assert "Test Meeting" in json_str

        # Test JSON deserialization
        restored = CalcomBooking.model_validate_json(json_str)
        assert restored.title == booking.title


class TestModelValidationEdgeCases:
    """Test cases for edge cases in model validation"""

    def test_empty_string_handling(self):
        """Test handling of empty strings in optional fields"""
        now = datetime.now(timezone.utc)
        booking = CalcomBooking(
            id=123,
            title="Test Meeting",
            description="",  # Empty string
            start_time=now,
            end_time=now,
            event_type_id=456,
            uid="test-uid-123",
        )

        assert booking.description == ""

    def test_none_vs_empty_list(self):
        """Test difference between None and empty list for list fields"""
        now = datetime.now(timezone.utc)

        # With explicit empty list
        booking1 = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=now,
            end_time=now,
            attendees=[],
            event_type_id=456,
            uid="test-uid-123",
        )

        # With default (should also be empty list)
        booking2 = CalcomBooking(
            id=123,
            title="Test Meeting",
            start_time=now,
            end_time=now,
            event_type_id=456,
            uid="test-uid-123",
        )

        assert booking1.attendees == []
        assert booking2.attendees == []

    def test_large_numbers(self):
        """Test handling of large numbers"""
        large_id = 999999999999999

        booking = CalcomBooking(
            id=large_id,
            title="Test Meeting",
            start_time=datetime.now(timezone.utc),
            end_time=datetime.now(timezone.utc),
            event_type_id=large_id,
            uid="test-uid-123",
        )

        assert booking.id == large_id
        assert booking.event_type_id == large_id


if __name__ == "__main__":
    pytest.main([__file__])

"""
Shared test fixtures and configuration for Cal.com MCP Server tests.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List

from src.models import (
    CalcomBooking,
    CreateBookingRequest,
    EventType,
    BookingListResponse,
    EventTypeListResponse,
    CreateBookingResponse,
    AvailabilitySlot,
    AvailabilityRequest,
    AvailabilityResponse,
)
from src.config import Settings


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "unit: Unit tests")
    config.addinivalue_line("markers", "integration: Integration tests")
    config.addinivalue_line("markers", "slow: Slow running tests")
    config.addinivalue_line("markers", "network: Tests requiring network access")


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_settings():
    """Mock settings for testing."""
    with patch("src.config.settings") as mock_settings:
        mock_settings.calcom_api_key = "test-api-key"
        mock_settings.calcom_base_url = "https://api.cal.com/v2"
        mock_settings.mcp_server_name = "Test Cal.com MCP Server"
        mock_settings.mcp_server_port = 8000
        mock_settings.mcp_server_host = "127.0.0.1"
        mock_settings.log_level = "DEBUG"
        yield mock_settings


@pytest.fixture
def test_settings():
    """Create test settings instance."""
    return Settings(
        calcom_api_key="test-api-key",
        calcom_base_url="https://api.cal.com/v2",
        mcp_server_name="Test Cal.com MCP Server",
        mcp_server_port=8000,
        mcp_server_host="127.0.0.1",
        log_level="DEBUG",
    )


@pytest.fixture
def sample_datetime():
    """Sample datetime for testing."""
    return datetime(2024, 1, 15, 14, 0, 0, tzinfo=timezone.utc)


@pytest.fixture
def sample_booking(sample_datetime):
    """Sample CalcomBooking for testing."""
    return CalcomBooking(
        id=123,
        title="Test Meeting",
        description="A test meeting",
        start_time=sample_datetime,
        end_time=sample_datetime + timedelta(hours=1),
        attendees=[
            {"name": "John Doe", "email": "<EMAIL>"},
            {"name": "Jane Smith", "email": "<EMAIL>"},
        ],
        location="Zoom Meeting",
        event_type_id=456,
        status="confirmed",
        uid="test-booking-uid-123",
    )


@pytest.fixture
def sample_event_type():
    """Sample EventType for testing."""
    return EventType(
        id=456,
        title="30 Min Meeting",
        slug="30min-meeting",
        length=30,
        description="Quick 30-minute meeting",
        locations=[
            {"type": "zoom", "url": "https://zoom.us"},
            {"type": "phone", "number": "+1234567890"},
        ],
    )


@pytest.fixture
def sample_booking_request():
    """Sample CreateBookingRequest for testing."""
    return CreateBookingRequest(
        event_type_id=456,
        start="2024-01-15T14:00:00",
        end="2024-01-15T15:00:00",
        attendee_name="John Doe",
        attendee_email="<EMAIL>",
        attendee_timezone="UTC",
        location="Zoom Meeting",
        notes="Test meeting notes",
        language="en",
    )


@pytest.fixture
def sample_availability_request():
    """Sample AvailabilityRequest for testing."""
    return AvailabilityRequest(
        event_type_id=456,
        date_from="2024-01-15",
        date_to="2024-01-15",
        timezone="UTC",
    )


@pytest.fixture
def sample_availability_slots():
    """Sample availability slots for testing."""
    return [
        AvailabilitySlot(
            time="2024-01-15T09:00:00Z - 2024-01-15T09:30:00Z",
            attendees=0,
            booking_url="https://cal.com/book/slot1",
        ),
        AvailabilitySlot(
            time="2024-01-15T10:00:00Z - 2024-01-15T10:30:00Z",
            attendees=1,
            booking_url="https://cal.com/book/slot2",
        ),
        AvailabilitySlot(
            time="2024-01-15T14:00:00Z - 2024-01-15T14:30:00Z",
            attendees=0,
            booking_url="https://cal.com/book/slot3",
        ),
    ]


@pytest.fixture
def mock_booking_list_response(sample_booking):
    """Mock BookingListResponse for testing."""
    return BookingListResponse(
        bookings=[sample_booking],
        total_count=1,
        message="Successfully retrieved bookings",
    )


@pytest.fixture
def mock_event_type_list_response(sample_event_type):
    """Mock EventTypeListResponse for testing."""
    return EventTypeListResponse(
        event_types=[sample_event_type],
        total_count=1,
        message="Successfully retrieved event types",
    )


@pytest.fixture
def mock_create_booking_response():
    """Mock CreateBookingResponse for testing."""
    return CreateBookingResponse(
        success=True,
        booking_id=789,
        booking_uid="created-booking-uid-789",
        booking_url="https://zoom.us/j/123456789",
        message="Booking created successfully",
    )


@pytest.fixture
def mock_availability_response(sample_availability_slots):
    """Mock AvailabilityResponse for testing."""
    return AvailabilityResponse(
        success=True,
        event_type_id=456,
        date_from="2024-01-15",
        date_to="2024-01-15",
        available_slots=sample_availability_slots,
        timezone="UTC",
        message="Found 3 available slots",
        total_slots=3,
    )


@pytest.fixture
def mock_cal_client():
    """Mock CalcomClient for testing."""
    client = AsyncMock()
    client.base_url = "https://api.cal.com/v2"
    client.api_key = "test-api-key"
    return client


@pytest.fixture
def mock_cal_client_context(mock_cal_client):
    """Mock CalcomClient context manager for testing."""
    with patch("src.cal_client.CalcomClient") as mock_client_class:
        mock_client_class.return_value.__aenter__.return_value = mock_cal_client
        mock_client_class.return_value.__aexit__.return_value = None
        yield mock_cal_client


@pytest.fixture
def mock_user_info():
    """Mock user info for testing."""
    return {
        "id": 123,
        "username": "testuser",
        "email": "<EMAIL>",
        "name": "Test User",
        "timeZone": "UTC",
    }


@pytest.fixture
def mock_context():
    """Mock FastMCP Context for testing."""
    context = AsyncMock()
    context.info = AsyncMock()
    context.error = AsyncMock()
    context.warning = AsyncMock()
    return context


@pytest.fixture
def mock_http_response():
    """Mock HTTP response for testing."""
    response = MagicMock()
    response.status_code = 200
    response.raise_for_status.return_value = None
    response.json.return_value = {"status": "success", "data": []}
    response.text = "Success"
    return response


@pytest.fixture
def mock_error_response():
    """Mock HTTP error response for testing."""
    response = MagicMock()
    response.status_code = 400
    response.json.return_value = {
        "message": "Bad Request",
        "error": "validation_error",
    }
    response.text = "Bad Request"
    return response


@pytest.fixture
def sample_booking_data():
    """Sample raw booking data as returned by Cal.com API."""
    return {
        "id": 123,
        "title": "Test Meeting",
        "description": "A test meeting",
        "startTime": "2024-01-15T14:00:00Z",
        "endTime": "2024-01-15T15:00:00Z",
        "attendees": [
            {"name": "John Doe", "email": "<EMAIL>"},
        ],
        "location": "Zoom Meeting",
        "eventTypeId": 456,
        "status": "confirmed",
        "uid": "test-booking-uid-123",
    }


@pytest.fixture
def sample_event_type_data():
    """Sample raw event type data as returned by Cal.com API."""
    return {
        "id": 456,
        "title": "30 Min Meeting",
        "slug": "30min-meeting",
        "length": 30,
        "description": "Quick 30-minute meeting",
        "locations": [
            {"type": "zoom", "url": "https://zoom.us"},
        ],
    }


@pytest.fixture
def multiple_bookings(sample_datetime):
    """Multiple sample bookings for testing."""
    bookings = []
    for i in range(5):
        booking = CalcomBooking(
            id=100 + i,
            title=f"Test Meeting {i + 1}",
            start_time=sample_datetime + timedelta(hours=i),
            end_time=sample_datetime + timedelta(hours=i + 1),
            attendees=[{"name": f"User {i + 1}", "email": f"user{i + 1}@example.com"}],
            event_type_id=456,
            uid=f"test-uid-{100 + i}",
        )
        bookings.append(booking)
    return bookings


@pytest.fixture
def multiple_event_types():
    """Multiple sample event types for testing."""
    event_types = []
    durations = [15, 30, 45, 60]
    for i, duration in enumerate(durations):
        event_type = EventType(
            id=400 + i,
            title=f"{duration} Min Meeting",
            slug=f"{duration}min",
            length=duration,
            description=f"{duration}-minute meeting",
        )
        event_types.append(event_type)
    return event_types


@pytest.fixture(autouse=True)
def reset_mocks():
    """Reset all mocks after each test."""
    yield
    # This fixture runs after each test to ensure clean state


@pytest.fixture
def api_error_scenarios():
    """Common API error scenarios for testing."""
    return {
        "network_error": Exception("Network connection failed"),
        "timeout_error": asyncio.TimeoutError("Request timeout"),
        "auth_error": Exception("Authentication failed"),
        "not_found": Exception("Resource not found"),
        "rate_limit": Exception("Rate limit exceeded"),
        "server_error": Exception("Internal server error"),
    }


@pytest.fixture
def cal_api_responses():
    """Common Cal.com API response formats for testing."""
    return {
        "v1_bookings": {
            "bookings": [
                {
                    "id": 123,
                    "title": "V1 Meeting",
                    "startTime": "2024-01-15T14:00:00Z",
                    "endTime": "2024-01-15T15:00:00Z",
                    "eventTypeId": 456,
                    "uid": "v1-uid-123",
                }
            ]
        },
        "v2_bookings": {
            "status": "success",
            "data": [
                {
                    "id": 124,
                    "title": "V2 Meeting",
                    "start": "2024-01-15T15:00:00Z",
                    "end": "2024-01-15T16:00:00Z",
                    "eventTypeId": 456,
                    "uid": "v2-uid-124",
                }
            ],
        },
        "v2_event_types": {
            "status": "success",
            "data": {
                "eventTypeGroups": [
                    {
                        "eventTypes": [
                            {
                                "id": 456,
                                "title": "30 Min Meeting",
                                "slug": "30min",
                                "length": 30,
                            }
                        ]
                    }
                ]
            },
        },
        "availability": {
            "dateRanges": [
                {"start": "2024-01-15T09:00:00Z", "end": "2024-01-15T09:30:00Z"}
            ]
        },
    }


# Utility functions for tests
def assert_booking_equal(booking1: CalcomBooking, booking2: CalcomBooking):
    """Assert that two bookings are equal."""
    assert booking1.id == booking2.id
    assert booking1.title == booking2.title
    assert booking1.uid == booking2.uid
    assert booking1.event_type_id == booking2.event_type_id


def assert_event_type_equal(et1: EventType, et2: EventType):
    """Assert that two event types are equal."""
    assert et1.id == et2.id
    assert et1.title == et2.title
    assert et1.slug == et2.slug
    assert et1.length == et2.length


def create_mock_response(data: Dict[str, Any], status_code: int = 200):
    """Create a mock HTTP response."""
    response = MagicMock()
    response.status_code = status_code
    response.json.return_value = data
    response.text = str(data)
    if status_code >= 400:
        response.raise_for_status.side_effect = Exception(f"HTTP {status_code}")
    else:
        response.raise_for_status.return_value = None
    return response

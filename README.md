# Cal.com MCP Server

A streamable HTTP MCP (Model Context Protocol) server for Cal.com, built with [FastMCP](https://github.com/context7/fastmcp). This server allows you to fetch, create, and manage Cal.com bookings and event types using only the Cal.com API.

## Features

- Fetch Cal.com bookings (with filters)
- Create new bookings with intelligent error handling
- Check availability before booking to prevent conflicts (with automatic user detection)
- Get current user information for debugging and verification
- List available event types
- Streamable HTTP endpoints for real-time integration
- Simple configuration via `.env` file

## Project Structure

```
cal-com-mcp-server/
├── src/
│   ├── __init__.py
│   ├── server.py              # Main FastMCP server
│   ├── cal_client.py          # Cal.com API client
│   ├── models.py              # Pydantic models
│   └── config.py              # Configuration
├── tests/
│   ├── __init__.py
│   └── test_server.py
├── .env                       # Environment variables
├── requirements.txt
├── run_server.py              # Server entry point
└── README.md
```

## Prerequisites

- Python 3.9+
- A Cal.com API key ([see how to get one](https://cal.com/docs/api/authentication))

## Setup

1. **Clone the repository**

   ```bash
   git clone <your-repo-url>
   cd cal-com-mcp-server
   ```

2. **Create and activate a virtual environment (recommended)**

   ```bash
   python -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   pip install pydantic-settings  # Required for Pydantic v2+
   ```

4. **Configure environment variables**

   - Copy `.env.example` to `.env` (or create `.env`):
     ```env
     CALCOM_API_KEY=cal_test_your_api_key_here
     CALCOM_BASE_URL=https://api.cal.com/v2
     MCP_SERVER_NAME=Cal.com MCP Server
     MCP_SERVER_PORT=8000
     MCP_SERVER_HOST=localhost
     LOG_LEVEL=INFO
     ```
   - Replace `CALCOM_API_KEY` with your actual Cal.com API key.

5. **Run the server**
   ```bash
   python run_server.py
   ```
   The server will start on the host and port specified in your `.env` (default: `localhost:8000`).

## Usage

### Health Check

```bash
curl http://localhost:8000/health
```

### Get Event Types

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "get_calcom_event_types",
      "arguments": {}
    }
  }'
```

### Fetch Bookings

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "fetch_calcom_bookings",
      "arguments": {
        "limit": 5,
        "status": "confirmed"
      }
    }
  }'
```

### Check Availability (Recommended before booking)

**Note**: The availability tool now automatically detects your user information - no need to provide `username`, `user_id`, or `team_id`.

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "check_calcom_availability",
      "arguments": {
        "event_type_id": 123,
        "date_from": "2024-01-15",
        "date_to": "2024-01-15"
      }
    }
  }'
```

### Get User Information (For debugging)

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 5,
    "method": "tools/call",
    "params": {
      "name": "get_calcom_user_info",
      "arguments": {}
    }
  }'
```

### Create Booking

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
      "name": "create_calcom_booking",
      "arguments": {
        "event_type_id": 123,
        "start_time": "2024-01-15T14:00:00",
        "end_time": "2024-01-15T15:00:00",
        "attendee_name": "Jane Smith",
        "attendee_email": "<EMAIL>",
        "location": "Conference Room A"
      }
    }
  }'
```

## Enhanced Error Handling

This server includes intelligent error handling for common Cal.com booking scenarios:

### Error Types Detected

- **`no_available_users_found_error`**: No available time slots for the requested booking time
- **`event_type_not_found`**: Invalid or non-existent event type ID
- **`invalid_time`**: Past dates or invalid time formats

### Best Practices

1. **Check availability first**: Use `check_calcom_availability` before creating bookings (user detection is automatic)
2. **Verify your account**: Use `get_calcom_user_info` to confirm which Cal.com account is being used
3. **Handle errors gracefully**: The server provides contextual suggestions for failed bookings
4. **Use proper time formats**: Ensure dates are in ISO format (YYYY-MM-DDTHH:MM:SS)

### Python Client Example

See the guide or use the [fastmcp](https://github.com/context7/fastmcp) client to interact programmatically.

## Docker

You can also run the server in Docker:

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "run_server.py"]
```

## Key Benefits

- **Unified API**: All operations go through Cal.com's API
- **Automatic User Detection**: No need to manually provide user credentials for availability checks
- **Automatic Integration**: Cal.com handles calendar syncing
- **Notification Management**: Cal.com sends emails and reminders
- **Booking Logic**: Cal.com handles availability, conflicts, and scheduling rules
- **Consistent Data**: All booking data is centralized in Cal.com

## License

MIT

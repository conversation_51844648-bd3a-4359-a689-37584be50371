# Cal.com MCP Server (AI Agent Optimized)

A simplified, AI-agent friendly MCP (Model Context Protocol) server for Cal.com, built with [FastMCP](https://github.com/jlowin/fastmcp). This server provides easy-to-use tools for AI agents to manage Cal.com bookings with minimal complexity.

## Features

- **Simple Tools**: Only required parameters, clear descriptions
- **AI-Agent Friendly**: Designed specifically for AI agent usability
- **Text-Based Responses**: All tools return simple text instead of complex JSON
- **Minimal Configuration**: Just add your Cal.com API key
- **FastMCP Best Practices**: Follows modern MCP server patterns

## Available Tools

1. **`list_bookings`** - Get all bookings (no parameters needed)
2. **`create_booking`** - Create a new booking (5 required parameters only)
3. **`list_event_types`** - Get available event types (no parameters needed)
4. **`check_availability`** - Check available time slots (2 required parameters only)

## Project Structure

```
cal-com-mcp-server/
├── src/
│   ├── __init__.py
│   ├── server.py              # Main FastMCP server
│   ├── cal_client.py          # Cal.com API client
│   ├── models.py              # Pydantic models
│   └── config.py              # Configuration
├── tests/
│   ├── __init__.py
│   └── test_server.py
├── .env                       # Environment variables
├── requirements.txt
├── run_server.py              # Server entry point
└── README.md
```

## Prerequisites

- Python 3.9+
- A Cal.com API key ([see how to get one](https://cal.com/docs/api/authentication))

## Setup

1. **Clone the repository**

   ```bash
   git clone <your-repo-url>
   cd cal-com-mcp-server
   ```

2. **Create and activate a virtual environment (recommended)**

   ```bash
   python -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   pip install pydantic-settings  # Required for Pydantic v2+
   ```

4. **Configure environment variables**

   - Copy `.env.example` to `.env` (or create `.env`):
     ```env
     CALCOM_API_KEY=cal_test_your_api_key_here
     CALCOM_BASE_URL=https://api.cal.com/v2
     MCP_SERVER_NAME=Cal.com MCP Server
     MCP_SERVER_PORT=8000
     MCP_SERVER_HOST=localhost
     LOG_LEVEL=INFO
     ```
   - Replace `CALCOM_API_KEY` with your actual Cal.com API key.

5. **Run the server**
   ```bash
   python run_server.py
   ```
   The server will start on the host and port specified in your `.env` (default: `localhost:8000`).

## Usage

### Health Check

```bash
curl http://localhost:8000/health
```

### List Event Types

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "list_event_types",
      "arguments": {}
    }
  }'
```

### List Bookings

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "list_bookings",
      "arguments": {}
    }
  }'
```

### Check Availability

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "check_availability",
      "arguments": {
        "event_type_id": 123,
        "date": "2024-01-15"
      }
    }
  }'
```

### Create Booking

```bash
curl -X POST http://localhost:8000/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
      "name": "create_booking",
      "arguments": {
        "event_type_id": 123,
        "start_time": "2024-01-15T14:00:00",
        "end_time": "2024-01-15T15:00:00",
        "attendee_name": "Jane Smith",
        "attendee_email": "<EMAIL>"
      }
    }
  }'
```

## AI Agent Best Practices

This server is optimized for AI agents with these design principles:

### Simplified Design

- **Only required parameters**: No optional parameters to confuse AI agents
- **Text responses**: All tools return simple text instead of complex JSON structures
- **Clear tool names**: `list_bookings`, `create_booking`, `list_event_types`, `check_availability`
- **Consistent patterns**: All tools follow the same simple input/output pattern

### Recommended Workflow

1. **List event types**: Use `list_event_types` to see available meeting types
2. **Check availability**: Use `check_availability` to find open time slots
3. **Create booking**: Use `create_booking` with the exact parameters needed
4. **Verify booking**: Use `list_bookings` to confirm the booking was created

### Error Handling

- All errors return simple text messages
- No complex error codes or nested structures
- Clear guidance on what went wrong and how to fix it

## Tool Reference

### `list_bookings`

- **Parameters**: None
- **Returns**: Text list of all bookings from the past 30 days and next 30 days
- **Example**: "Found 3 bookings: • Meeting with John - 2024-01-15 14:00 (confirmed)"

### `create_booking`

- **Parameters**:
  - `event_type_id` (int): Event type ID from `list_event_types`
  - `start_time` (string): Start time in ISO format (e.g., "2024-01-15T14:00:00")
  - `end_time` (string): End time in ISO format (e.g., "2024-01-15T15:00:00")
  - `attendee_name` (string): Full name of attendee
  - `attendee_email` (string): Email address of attendee
- **Returns**: Text confirmation with booking UID
- **Example**: "✅ Successfully created booking for John Doe from 2024-01-15T14:00:00 to 2024-01-15T15:00:00. Booking UID: abc123"

### `list_event_types`

- **Parameters**: None
- **Returns**: Text list of available event types with IDs
- **Example**: "Found 2 event types: • ID: 123 - 30 Minute Meeting (30 minutes) • ID: 456 - Quick Chat (15 minutes)"

### `check_availability`

- **Parameters**:
  - `event_type_id` (int): Event type ID from `list_event_types`
  - `date` (string): Date to check in YYYY-MM-DD format (e.g., "2024-01-15")
- **Returns**: Text list of available time slots
- **Example**: "Found 3 available time slots on 2024-01-15: • 2024-01-15T09:00:00 • 2024-01-15T10:00:00 • 2024-01-15T11:00:00"

## Docker

You can also run the server in Docker:

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "run_server.py"]
```

## Key Benefits

- **AI-Agent Optimized**: Designed specifically for AI agent usability
- **Simple Interface**: Only required parameters, clear text responses
- **FastMCP Best Practices**: Follows modern MCP server patterns
- **Automatic User Detection**: No complex authentication setup needed
- **Cal.com Integration**: Leverages Cal.com's booking logic and notifications

## License

MIT

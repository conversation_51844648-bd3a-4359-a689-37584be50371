import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any, Union
from fastmcp import Fast<PERSON><PERSON>, Context
from starlette.requests import Request
from starlette.responses import JSONResponse

from .config import settings
from .cal_client import CalcomClient
from .models import CreateBookingRequest, AvailabilityRequest

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP(name=settings.mcp_server_name)


@mcp.tool
async def fetch_calcom_bookings(
    limit: int = 100,
    status: Optional[str] = None,
    days_back: int = 30,
    days_forward: int = 30,
    ctx: Context = None,
) -> Dict[str, Any]:
    """
    Fetch bookings from Cal.com within a specified date range.

    Args:
        limit: Maximum number of bookings to return (integer, 1-1000, default: 100)
        status: Filter by booking status (optional string). Valid values:
            - "confirmed" - Confirmed bookings
            - "pending" - Pending confirmation bookings
            - "cancelled" - Cancelled bookings
            - "rescheduled" - Rescheduled bookings
            - Leave empty/null to fetch all statuses
        days_back: Number of days in the past to include (integer, 0-365, default: 30)
        days_forward: Number of days in the future to include (integer, 0-365, default: 30)

    Returns:
        Dictionary with structured content containing:
        - success: Boolean indicating if fetch was successful
        - bookings: Array of booking objects with fields:
            - id: Booking ID
            - title: Booking title
            - start_time: Start datetime (ISO format)
            - end_time: End datetime (ISO format)
            - attendees: Array of attendee objects
            - location: Meeting location
            - status: Booking status
            - uid: Unique booking identifier
        - total_count: Number of bookings returned
        - date_range: Date range used for the query
        - filters: Applied filters (status, limit)
    """
    try:
        if ctx:
            await ctx.info(f"Fetching Cal.com bookings...")

        # Calculate date range
        now = datetime.now(timezone.utc)
        start_date = now - timedelta(days=days_back)
        end_date = now + timedelta(days=days_forward)

        # Fetch bookings from Cal.com
        async with CalcomClient() as cal_client:
            bookings_response = await cal_client.get_bookings(
                limit=limit, status=status, start_date=start_date, end_date=end_date
            )

        if ctx:
            await ctx.info(
                f"Successfully fetched {len(bookings_response.bookings)} bookings"
            )

        # Prepare structured content
        structured_data = {
            "success": True,
            "bookings": [b.model_dump() for b in bookings_response.bookings],
            "total_count": bookings_response.total_count,
            "message": bookings_response.message,
            "date_range": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            },
            "filters": {"status": status, "limit": limit},
        }

        # Create human-readable text content
        text_content = (
            f"Successfully fetched {len(bookings_response.bookings)} Cal.com bookings"
        )
        if status:
            text_content += f" with status '{status}'"
        text_content += f" from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"

        if bookings_response.bookings:
            text_content += "\n\nBookings summary:"
            for booking in bookings_response.bookings[:5]:  # Show first 5 bookings
                text_content += f"\n- {booking.title} ({booking.start_time.strftime('%Y-%m-%d %H:%M')} - {booking.end_time.strftime('%H:%M')})"
            if len(bookings_response.bookings) > 5:
                text_content += (
                    f"\n... and {len(bookings_response.bookings) - 5} more bookings"
                )

        # Return structured response with both content and structuredContent
        return {
            "content": [{"type": "text", "text": text_content}],
            "structuredContent": structured_data,
        }

    except Exception as e:
        error_msg = f"Error fetching Cal.com bookings: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)

        error_data = {
            "success": False,
            "bookings": [],
            "total_count": 0,
            "error": error_msg,
            "filters": {"status": status, "limit": limit},
        }

        return {
            "content": [{"type": "text", "text": error_msg}],
            "structuredContent": error_data,
            "isError": True,
        }


@mcp.tool
async def create_calcom_booking(
    event_type_id: int,
    start_time: str,
    end_time: str,
    attendee_name: str,
    attendee_email: str,
    attendee_timezone: str = "UTC",
    location: Optional[str] = None,
    notes: Optional[str] = None,
    language: str = "en",
    ctx: Context = None,
) -> Dict[str, Any]:
    """
    Create a new booking in Cal.com.

    Args:
        event_type_id: Cal.com Event Type ID (integer, e.g., 123456)
        start_time: Booking start time. Accepts multiple formats:
            - Full ISO format: "2024-01-15T14:00:00" or "2024-01-15T14:00:00Z"
            - Time only: "14:00" or "14:00:00" (will use tomorrow's date)
            - Date with time: "2024-01-15T14:00"
        end_time: Booking end time. Same formats as start_time:
            - Full ISO format: "2024-01-15T15:00:00" or "2024-01-15T15:00:00Z"
            - Time only: "15:00" or "15:00:00" (will use tomorrow's date)
            - Date with time: "2024-01-15T15:00"
        attendee_name: Full name of the person booking (string, e.g., "John Doe")
        attendee_email: Valid email address of the attendee (string, e.g., "<EMAIL>")
        attendee_timezone: Timezone identifier (default: "UTC"). Examples:
            - "UTC", "America/New_York", "Europe/London", "Asia/Tokyo"
        location: Meeting location (optional string). Examples:
            - "Conference Room A", "Zoom", "Google Meet", "Phone call"
        notes: Additional notes or agenda for the booking (optional string)
        language: Language preference (default: "en"). Examples: "en", "es", "fr", "de"

    Returns:
        Dictionary with structured content containing:
        - success: Boolean indicating if booking was created
        - booking_id: Cal.com booking ID (if successful)
        - booking_uid: Unique booking identifier (if successful)
        - booking_url: Meeting URL or location details (if available)
        - message: Success or error message
        - request_details: Original request parameters for reference
    """
    try:
        if ctx:
            await ctx.info(f"Creating Cal.com booking for {attendee_name}")

        # Parse and validate datetime strings
        def parse_datetime(time_str: str) -> str:
            """Parse time string and convert to proper ISO format"""
            # If it's just a time (HH:MM), assume it's for tomorrow
            if ":" in time_str and "T" not in time_str and len(time_str) <= 8:
                # It's just a time, add tomorrow's date
                tomorrow = datetime.now(timezone.utc) + timedelta(days=1)
                date_part = tomorrow.strftime("%Y-%m-%d")
                return f"{date_part}T{time_str}:00"

            # If it already looks like an ISO string, return as is
            if "T" in time_str:
                # Ensure it has seconds
                if time_str.count(":") == 1:
                    time_str += ":00"
                return time_str

            # If it's a date without time, add default time
            if "-" in time_str and "T" not in time_str:
                return f"{time_str}T09:00:00"

            return time_str

        # Parse the datetime strings
        parsed_start_time = parse_datetime(start_time)
        parsed_end_time = parse_datetime(end_time)

        # Create booking request
        booking_request = CreateBookingRequest(
            event_type_id=event_type_id,
            start=parsed_start_time,
            end=parsed_end_time,
            attendee_name=attendee_name,
            attendee_email=attendee_email,
            attendee_timezone=attendee_timezone,
            location=location,
            notes=notes,
            language=language,
        )

        # Create booking using Cal.com client
        async with CalcomClient() as cal_client:
            result = await cal_client.create_booking(booking_request)

        if ctx:
            if result.success:
                await ctx.info(f"Successfully created booking: {result.booking_uid}")
            else:
                await ctx.error(f"Failed to create booking: {result.message}")

        # Prepare structured content
        structured_data = result.model_dump()
        structured_data.update(
            {
                "request_details": {
                    "event_type_id": event_type_id,
                    "start_time": parsed_start_time,  # Show parsed time
                    "end_time": parsed_end_time,  # Show parsed time
                    "original_start_time": start_time,  # Keep original for reference
                    "original_end_time": end_time,  # Keep original for reference
                    "attendee_name": attendee_name,
                    "attendee_email": attendee_email,
                    "attendee_timezone": attendee_timezone,
                    "location": location,
                    "notes": notes,
                    "language": language,
                }
            }
        )

        # Create human-readable text content
        if result.success:
            text_content = (
                f"✅ Successfully created Cal.com booking for {attendee_name}"
            )
            if result.booking_uid:
                text_content += f"\nBooking UID: {result.booking_uid}"
            if result.booking_url:
                text_content += f"\nBooking URL: {result.booking_url}"
            text_content += f"\nScheduled: {parsed_start_time} to {parsed_end_time} ({attendee_timezone})"
            if location:
                text_content += f"\nLocation: {location}"
            if notes:
                text_content += f"\nNotes: {notes}"
        else:
            text_content = f"❌ Failed to create Cal.com booking for {attendee_name}: {result.message}"

            # Add helpful suggestions based on error type
            if result.error_code == "no_available_users_found_error":
                text_content += f"\n\n💡 Suggestion: Use the 'check_calcom_availability' tool to find available time slots for event type {event_type_id}"
                if result.error_details:
                    text_content += f"\nRequested time: {result.error_details.get('requested_time', 'N/A')}"
            elif result.error_code == "event_type_not_found":
                text_content += f"\n\n💡 Suggestions:"
                text_content += f"\n1. Use the 'get_calcom_event_types' tool to find valid event type IDs for your account"
                text_content += f"\n2. Verify that event type {event_type_id} exists and belongs to your Cal.com account"
                text_content += f"\n3. Check if you have the necessary permissions to access this event type"
                text_content += f"\n4. If this is a team event type, ensure you're a member of the team"
            elif result.error_code == "invalid_time":
                text_content += f"\n\n💡 Suggestion: Ensure times are in the future and in valid ISO format (YYYY-MM-DDTHH:MM:SS)"
            elif result.error_code == "image_processing_warning":
                text_content += f"\n\n⚠️ Note: This appears to be a Cal.com server-side image processing issue (sharp module)."
                text_content += f"\n💡 Suggestions:"
                text_content += f"\n1. Check if the booking was actually created using the 'fetch_calcom_bookings' tool"
                text_content += f"\n2. This is typically a non-critical warning that doesn't prevent booking creation"
                text_content += f"\n3. The issue is on Cal.com's server side and may resolve automatically"
                text_content += (
                    f"\n4. If the booking wasn't created, try again in a few minutes"
                )

        return {
            "content": [{"type": "text", "text": text_content}],
            "structuredContent": structured_data,
            "isError": not result.success,
        }

    except ValueError as e:
        error_msg = f"Invalid input data: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)

        error_data = {
            "success": False,
            "error": error_msg,
            "error_type": "validation_error",
            "request_details": {
                "event_type_id": event_type_id,
                "start_time": start_time,
                "end_time": end_time,
                "attendee_name": attendee_name,
                "attendee_email": attendee_email,
                "attendee_timezone": attendee_timezone,
                "location": location,
                "notes": notes,
                "language": language,
            },
        }

        return {
            "content": [{"type": "text", "text": f"❌ {error_msg}"}],
            "structuredContent": error_data,
            "isError": True,
        }
    except Exception as e:
        error_msg = f"Error creating Cal.com booking: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)

        error_data = {
            "success": False,
            "error": error_msg,
            "error_type": "system_error",
            "request_details": {
                "event_type_id": event_type_id,
                "start_time": start_time,
                "end_time": end_time,
                "attendee_name": attendee_name,
                "attendee_email": attendee_email,
                "attendee_timezone": attendee_timezone,
                "location": location,
                "notes": notes,
                "language": language,
            },
        }

        return {
            "content": [{"type": "text", "text": f"❌ {error_msg}"}],
            "structuredContent": error_data,
            "isError": True,
        }


@mcp.tool
async def get_calcom_event_types(ctx: Context = None) -> Dict[str, Any]:
    """
    Get available Cal.com event types for booking creation.

    Args:
        No parameters required - fetches all available event types for the authenticated user.

    Returns:
        Dictionary with structured content containing:
        - success: Boolean indicating if fetch was successful
        - event_types: Array of event type objects with fields:
            - id: Event type ID (integer, use this for create_calcom_booking)
            - title: Event type name/title (string)
            - slug: URL-friendly identifier (string)
            - length: Duration in minutes (integer)
            - description: Event type description (string, optional)
            - locations: Array of available location options
        - total_count: Number of event types available
        - summary: Statistics including:
            - total_types: Count of event types
            - average_duration: Average meeting duration
            - types_by_duration: Grouping of event types by duration
    """
    try:
        if ctx:
            await ctx.info("Fetching Cal.com event types...")

        async with CalcomClient() as cal_client:
            event_types_response = await cal_client.get_event_types()

        event_types = event_types_response.event_types

        # Prepare structured content
        structured_data = {
            "success": True,
            "event_types": [et.model_dump() for et in event_types],
            "total_count": event_types_response.total_count,
            "message": event_types_response.message,
            "summary": {
                "total_types": len(event_types),
                "average_duration": (
                    sum(et.length for et in event_types) / len(event_types)
                    if event_types
                    else 0
                ),
                "types_by_duration": {},
            },
        }

        # Group event types by duration for summary
        if event_types:
            duration_groups = {}
            for et in event_types:
                duration = et.length
                if duration not in duration_groups:
                    duration_groups[duration] = []
                duration_groups[duration].append(et.title)
            structured_data["summary"]["types_by_duration"] = duration_groups

        # Create human-readable text content
        text_content = f"📅 Retrieved {len(event_types)} Cal.com event types"

        if event_types:
            text_content += "\n\nAvailable event types:"
            for et in event_types:
                text_content += f"\n- {et.title} ({et.length} min)"
                if et.description:
                    text_content += f" - {et.description[:100]}{'...' if len(et.description) > 100 else ''}"

            # Add summary statistics
            avg_duration = sum(et.length for et in event_types) / len(event_types)
            text_content += f"\n\nSummary: {len(event_types)} event types, average duration: {avg_duration:.1f} minutes"
        else:
            text_content += "\n\nNo event types found."

        return {
            "content": [{"type": "text", "text": text_content}],
            "structuredContent": structured_data,
        }

    except Exception as e:
        error_msg = f"Error fetching Cal.com event types: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)

        error_data = {
            "success": False,
            "event_types": [],
            "total_count": 0,
            "error": error_msg,
            "error_type": "system_error",
        }

        return {
            "content": [{"type": "text", "text": f"❌ {error_msg}"}],
            "structuredContent": error_data,
            "isError": True,
        }


@mcp.tool
async def check_calcom_availability(
    event_type_id: int,
    date_from: str,
    date_to: Optional[str] = None,
    timezone: str = "UTC",
    ctx: Context = None,
) -> Dict[str, Any]:
    """
    Check available time slots for a Cal.com event type on a specific date range.

    The function automatically fetches the current user's information from Cal.com API,
    so you don't need to provide username, user_id, or team_id.

    Args:
        event_type_id: Cal.com Event Type ID (integer, e.g., 123456)
        date_from: Start date to check availability for in YYYY-MM-DD format (e.g., "2024-01-15")
        date_to: End date to check availability for in YYYY-MM-DD format (optional, defaults to date_from)
        timezone: Timezone for the availability check (default: "UTC"). Examples:
            - "UTC", "America/New_York", "Europe/London", "Asia/Tokyo"

    Returns:
        Dictionary with structured content containing:
        - success: Boolean indicating if availability check was successful
        - event_type_id: The event type ID that was checked
        - date: The date that was checked (YYYY-MM-DD format)
        - available_slots: Array of available time slot objects with fields:
            - time: Available time slot in ISO format
            - attendees: Number of attendees for this slot
            - booking_url: Direct booking URL (if available)
        - timezone: Timezone used for the check
        - total_slots: Total number of available time slots
        - message: Success or error message
        - suggestions: Helpful suggestions if no slots are available
        - user_info: Information about the user whose availability was checked
    """
    try:
        if ctx:
            await ctx.info(
                f"Checking availability for event type {event_type_id} from {date_from}"
            )

        # Set date_to to date_from if not provided
        if date_to is None:
            date_to = date_from

        # Validate date format
        try:
            datetime.strptime(date_from, "%Y-%m-%d")
            datetime.strptime(date_to, "%Y-%m-%d")
        except ValueError:
            raise ValueError("Dates must be in YYYY-MM-DD format (e.g., '2024-01-15')")

        # Create availability request (user info will be fetched automatically by the client)
        availability_request = AvailabilityRequest(
            event_type_id=event_type_id,
            date_from=date_from,
            date_to=date_to,
            timezone=timezone,
            username=None,
            user_id=None,
            team_id=None,
        )

        # Check availability using Cal.com client
        async with CalcomClient() as cal_client:
            availability = await cal_client.check_availability(availability_request)

            # Also get user info to include in response
            try:
                user_info = await cal_client.get_me()
            except Exception as e:
                logger.warning(f"Could not fetch user info for response: {e}")
                user_info = {"id": "unknown", "username": "unknown"}

        if ctx:
            if availability.success:
                await ctx.info(
                    f"Found {availability.total_slots} available slots for user {user_info.get('username', 'unknown')}"
                )
            else:
                await ctx.warning(f"Availability check failed: {availability.message}")

        # Prepare structured content
        structured_data = availability.model_dump()
        structured_data["user_info"] = user_info

        # Add helpful suggestions if no slots are available
        if availability.success and availability.total_slots == 0:
            structured_data["suggestions"] = [
                "Try checking a different date",
                "Check if the event type is active and properly configured",
                "Verify the timezone is correct",
                "Consider checking availability for the next few days",
            ]
        elif not availability.success:
            structured_data["suggestions"] = [
                "Verify the event type ID is correct using get_calcom_event_types",
                "Ensure the date is in the future",
                "Check if the Cal.com API credentials are valid",
            ]

        # Create simplified response - just return the list of available time ranges
        if availability.success:
            if availability.total_slots > 0:
                # Create simple list of time ranges
                time_ranges = []
                for slot in availability.available_slots:
                    if " - " in slot.time:
                        # Parse time range and format nicely
                        start_time_str, end_time_str = slot.time.split(" - ")
                        start_time = datetime.fromisoformat(
                            start_time_str.replace("Z", "+00:00")
                        )
                        end_time = datetime.fromisoformat(
                            end_time_str.replace("Z", "+00:00")
                        )

                        # Format with date and time for clarity
                        start_formatted = start_time.strftime("%Y-%m-%d %H:%M")
                        end_formatted = end_time.strftime("%Y-%m-%d %H:%M")
                        time_ranges.append(f"{start_formatted} - {end_formatted}")
                    else:
                        # Handle single time format (fallback)
                        slot_time = datetime.fromisoformat(
                            slot.time.replace("Z", "+00:00")
                        )
                        time_ranges.append(slot_time.strftime("%Y-%m-%d %H:%M"))

                text_content = (
                    f"Available time slots ({len(time_ranges)} found):\n\n"
                    + "\n".join([f"• {slot}" for slot in time_ranges])
                )

                # Simplified structured content
                simple_structured_data = {
                    "success": True,
                    "available_time_ranges": time_ranges,
                    "total_slots": len(time_ranges),
                }
            else:
                text_content = (
                    "No available time slots found for the specified date range."
                )
                simple_structured_data = {
                    "success": True,
                    "available_time_ranges": [],
                    "total_slots": 0,
                }
        else:
            text_content = f"Failed to check availability: {availability.message}"
            simple_structured_data = {
                "success": False,
                "available_time_ranges": [],
                "total_slots": 0,
                "error": availability.message,
            }

        return {
            "content": [{"type": "text", "text": text_content}],
            "structuredContent": simple_structured_data,
            "isError": not availability.success,
        }

    except ValueError as e:
        error_msg = f"Invalid input data: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)

        error_data = {
            "success": False,
            "event_type_id": event_type_id,
            "date_from": date_from,
            "date_to": date_to,
            "available_slots": [],
            "timezone": timezone,
            "total_slots": 0,
            "error": error_msg,
            "error_type": "validation_error",
        }

        return {
            "content": [{"type": "text", "text": f"❌ {error_msg}"}],
            "structuredContent": error_data,
            "isError": True,
        }
    except Exception as e:
        error_msg = str(e)
        logger.error(f"Error checking Cal.com availability: {error_msg}")

        # Provide specific guidance for common errors
        if "Either username or userId or teamId should be filled in" in error_msg:
            user_friendly_msg = (
                "❌ Authentication error: Could not automatically fetch user information from Cal.com API.\n\n"
                "💡 This usually means:\n"
                "- Your API key may be invalid or expired\n"
                "- There may be a network connectivity issue\n"
                "- The Cal.com API may be temporarily unavailable"
            )
        elif "no_available_users_found_error" in error_msg:
            user_friendly_msg = (
                "❌ No available users found for the specified criteria.\n\n"
                "💡 This usually means:\n"
                "- The user/team doesn't exist\n"
                "- The event type is not associated with the user/team\n"
                "- The user is not available during the requested time"
            )
        else:
            user_friendly_msg = f"❌ Error checking Cal.com availability: {error_msg}"

        if ctx:
            await ctx.error(error_msg)

        error_data = {
            "success": False,
            "event_type_id": event_type_id,
            "date_from": date_from,
            "date_to": date_to,
            "available_slots": [],
            "timezone": timezone,
            "total_slots": 0,
            "error": error_msg,
            "error_type": "system_error",
        }

        return {
            "content": [{"type": "text", "text": user_friendly_msg}],
            "structuredContent": error_data,
            "isError": True,
        }


# Health check endpoint
@mcp.custom_route("/health", methods=["GET"])
async def health_check(request: Request):
    """Health check endpoint"""
    return JSONResponse(
        {
            "status": "healthy",
            "server": settings.mcp_server_name,
            "timestamp": datetime.now().isoformat(),
            "api_base": settings.calcom_base_url,
        }
    )


# Resource for server information
@mcp.resource("resource://server/info")
def get_server_info() -> Dict[str, Any]:
    """Provides server information and capabilities"""
    return {
        "name": settings.mcp_server_name,
        "version": "1.0.0",
        "capabilities": [
            "fetch_calcom_bookings",
            "create_calcom_booking",
            "get_calcom_event_types",
            "check_calcom_availability",
            "diagnose_calendar_integration",
        ],
        "supported_apis": ["Cal.com API v2"],
        "endpoints": {"health_check": "/health"},
        "description": "MCP server for Cal.com booking management",
    }


# Resource template for booking details
@mcp.resource("calcom://bookings/{booking_uid}")
async def get_booking_details(booking_uid: str, ctx: Context = None) -> Dict[str, Any]:
    """Get detailed information about a specific Cal.com booking"""
    try:
        if ctx:
            await ctx.info(f"Fetching details for booking {booking_uid}")

        async with CalcomClient() as cal_client:
            booking = await cal_client.get_booking_by_uid(booking_uid)

        if booking:
            if ctx:
                await ctx.info(
                    f"Successfully fetched details for booking {booking_uid}"
                )

            # Prepare structured content for resource
            structured_data = {
                "success": True,
                "booking": booking.model_dump(),
                "booking_uid": booking_uid,
                "metadata": {
                    "fetched_at": datetime.now(timezone.utc).isoformat(),
                    "duration_minutes": int(
                        (booking.end_time - booking.start_time).total_seconds() / 60
                    ),
                    "status": booking.status,
                    "attendee_count": len(booking.attendees),
                },
            }

            return structured_data
        else:
            if ctx:
                await ctx.warning(f"Booking with UID {booking_uid} not found")
            return {
                "success": False,
                "error": f"Booking with UID {booking_uid} not found",
                "booking_uid": booking_uid,
                "metadata": {
                    "fetched_at": datetime.now(timezone.utc).isoformat(),
                    "error_type": "not_found",
                },
            }

    except Exception as e:
        error_msg = f"Failed to fetch booking details for {booking_uid}: {str(e)}"
        logger.error(error_msg)
        if ctx:
            await ctx.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "booking_uid": booking_uid,
            "metadata": {
                "fetched_at": datetime.now(timezone.utc).isoformat(),
                "error_type": "system_error",
            },
        }

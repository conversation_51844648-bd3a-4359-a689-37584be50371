import logging
from datetime import datetime, timedelta, timezone
from typing import List
from fastmcp import Fast<PERSON><PERSON>, Context
from starlette.requests import Request
from starlette.responses import JSONResponse

from .config import settings
from .cal_client import CalcomClient
from .models import CreateBookingRequest, AvailabilityRequest

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

# Initialize FastMCP server
mcp = FastMCP(name=settings.mcp_server_name)


@mcp.tool
async def list_bookings(ctx: Context) -> str:
    """
    Get all Cal.com bookings for the past 30 days and next 30 days.

    Returns a simple text summary of bookings.
    """
    try:
        await ctx.info("Fetching Cal.com bookings...")

        # Calculate date range (30 days back, 30 days forward)
        now = datetime.now(timezone.utc)
        start_date = now - timedelta(days=30)
        end_date = now + timedelta(days=30)

        # Fetch bookings using Cal.com client
        async with CalcomClient() as cal_client:
            bookings_response = await cal_client.get_bookings(
                limit=100,
                status=None,
                start_date=start_date,
                end_date=end_date,
            )

        bookings = bookings_response.bookings

        if not bookings:
            return "No bookings found in the past 30 days or next 30 days."

        # Create simple text summary
        result = f"Found {len(bookings)} bookings:\n\n"

        for booking in bookings:
            start_time_str = booking.start_time.strftime("%Y-%m-%d %H:%M")
            result += f"• {booking.title} - {start_time_str} ({booking.status})\n"

        await ctx.info(f"Successfully fetched {len(bookings)} bookings")
        return result

    except Exception as e:
        error_msg = f"Error fetching bookings: {str(e)}"
        logger.error(error_msg)
        await ctx.error(error_msg)
        return error_msg


@mcp.tool
async def create_booking(
    event_type_id: int,
    start_time: str,
    end_time: str,
    attendee_name: str,
    attendee_email: str,
    ctx: Context,
) -> str:
    """
    Create a new booking in Cal.com.

    Args:
        event_type_id: Cal.com Event Type ID (get this from list_event_types)
        start_time: Start time in ISO format (e.g., "2024-01-15T14:00:00")
        end_time: End time in ISO format (e.g., "2024-01-15T15:00:00")
        attendee_name: Full name of the attendee
        attendee_email: Email address of the attendee

    Returns:
        Simple text message indicating success or failure.
    """
    try:
        await ctx.info(f"Creating Cal.com booking for {attendee_name}")

        # Create booking request
        booking_request = CreateBookingRequest(
            event_type_id=event_type_id,
            start_time=start_time,
            end_time=end_time,
            attendee_name=attendee_name,
            attendee_email=attendee_email,
        )

        # Create booking using Cal.com client
        async with CalcomClient() as cal_client:
            result = await cal_client.create_booking(booking_request)

        if result.success:
            await ctx.info(f"Successfully created booking: {result.booking_uid}")
            return f"✅ Successfully created booking for {attendee_name} from {start_time} to {end_time}. Booking UID: {result.booking_uid}"
        else:
            await ctx.error(f"Failed to create booking: {result.message}")
            return f"❌ Failed to create booking: {result.message}"

    except Exception as e:
        error_msg = f"Error creating booking: {str(e)}"
        logger.error(error_msg)
        await ctx.error(error_msg)
        return error_msg


@mcp.tool
async def list_event_types(ctx: Context) -> str:
    """
    Get all available Cal.com event types.

    Returns a simple text list of event types with their IDs and names.
    """
    try:
        await ctx.info("Fetching Cal.com event types...")

        async with CalcomClient() as cal_client:
            event_types_response = await cal_client.get_event_types()

        event_types = event_types_response.event_types

        if not event_types:
            return "No event types found."

        # Create simple text list
        result = f"Found {len(event_types)} event types:\n\n"

        for et in event_types:
            result += f"• ID: {et.id} - {et.title} ({et.length} minutes)\n"

        await ctx.info(f"Successfully fetched {len(event_types)} event types")
        return result

    except Exception as e:
        error_msg = f"Error fetching event types: {str(e)}"
        logger.error(error_msg)
        await ctx.error(error_msg)
        return error_msg


@mcp.tool
async def check_availability(
    event_type_id: int,
    date: str,
    ctx: Context,
) -> str:
    """
    Check available time slots for a Cal.com event type on a specific date.

    Args:
        event_type_id: Cal.com Event Type ID (get this from list_event_types)
        date: Date to check in YYYY-MM-DD format (e.g., "2024-01-15")

    Returns:
        Simple text list of available time slots.
    """
    try:
        await ctx.info(
            f"Checking availability for event type {event_type_id} on {date}"
        )

        # Create availability request
        availability_request = AvailabilityRequest(
            event_type_id=event_type_id,
            date=date,
        )

        # Check availability using Cal.com client
        async with CalcomClient() as cal_client:
            availability = await cal_client.check_availability(availability_request)

        if not availability.success:
            await ctx.error(f"Availability check failed: {availability.message}")
            return f"❌ Failed to check availability: {availability.message}"

        if availability.total_slots == 0:
            return f"No available time slots found for event type {event_type_id} on {date}."

        # Create simple text list of available slots
        result = f"Found {availability.total_slots} available time slots on {date}:\n\n"

        for slot in availability.available_slots:
            result += f"• {slot.time}\n"

        await ctx.info(f"Found {availability.total_slots} available slots")
        return result

    except Exception as e:
        error_msg = f"Error checking availability: {str(e)}"
        logger.error(error_msg)
        await ctx.error(error_msg)
        return error_msg


# Health check endpoint
@mcp.custom_route("/health", methods=["GET"])
async def health_check(request: Request):
    """Health check endpoint"""
    return JSONResponse(
        {
            "status": "healthy",
            "server": settings.mcp_server_name,
            "timestamp": datetime.now().isoformat(),
            "api_base": settings.calcom_base_url,
        }
    )


# Resource for server information
@mcp.resource("resource://server/info")
def get_server_info():
    """Provides server information and capabilities"""
    return {
        "name": settings.mcp_server_name,
        "version": "2.0.0",
        "capabilities": [
            "list_bookings",
            "create_booking",
            "list_event_types",
            "check_availability",
        ],
        "supported_apis": ["Cal.com API v2"],
        "endpoints": {"health_check": "/health"},
        "description": "Simplified MCP server for Cal.com booking management - AI agent friendly",
    }


# Resource template for booking details
@mcp.resource("calcom://bookings/{booking_uid}")
async def get_booking_details(booking_uid: str, ctx: Context):
    """Get detailed information about a specific Cal.com booking"""
    try:
        await ctx.info(f"Fetching details for booking {booking_uid}")

        async with CalcomClient() as cal_client:
            booking = await cal_client.get_booking_by_uid(booking_uid)

        if booking:
            await ctx.info(f"Successfully fetched details for booking {booking_uid}")
            return {
                "success": True,
                "booking": booking.model_dump(),
                "booking_uid": booking_uid,
            }
        else:
            await ctx.warning(f"Booking with UID {booking_uid} not found")
            return {
                "success": False,
                "error": f"Booking with UID {booking_uid} not found",
                "booking_uid": booking_uid,
            }

    except Exception as e:
        error_msg = f"Failed to fetch booking details for {booking_uid}: {str(e)}"
        logger.error(error_msg)
        await ctx.error(error_msg)
        return {
            "success": False,
            "error": error_msg,
            "booking_uid": booking_uid,
        }

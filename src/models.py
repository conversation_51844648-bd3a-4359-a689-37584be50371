from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class CalcomBooking(BaseModel):
    """Represents a Cal.com booking"""

    id: int
    title: str
    description: Optional[str] = None
    start_time: datetime
    end_time: datetime
    attendees: List[Dict[str, Any]] = []
    location: Optional[str] = None
    event_type_id: int
    status: str = "confirmed"
    uid: str


class CreateBookingRequest(BaseModel):
    """Request model for creating a Cal.com booking"""

    event_type_id: int = Field(..., description="Cal.com Event Type ID")
    start: str = Field(..., description="Start time in ISO format")
    end: str = Field(..., description="End time in ISO format")
    attendee_name: str = Field(..., description="Attendee name")
    attendee_email: str = Field(..., description="Attendee email")
    attendee_timezone: str = Field("UTC", description="Attendee timezone")
    location: Optional[str] = Field(None, description="Meeting location")
    notes: Optional[str] = Field(None, description="Additional notes")
    language: str = Field("en", description="Language preference")


class EventType(BaseModel):
    """Represents a Cal.com Event Type"""

    id: int
    title: str
    slug: str
    length: int  # in minutes
    description: Optional[str] = None
    locations: List[Dict[str, Any]] = []


class BookingListResponse(BaseModel):
    """Response model for listing bookings"""

    bookings: List[CalcomBooking]
    total_count: int
    message: str


class EventTypeListResponse(BaseModel):
    """Response model for listing event types"""

    event_types: List[EventType]
    total_count: int
    message: str


class CreateBookingResponse(BaseModel):
    """Response model for booking creation"""

    success: bool
    booking_id: Optional[int] = None
    booking_uid: Optional[str] = None
    booking_url: Optional[str] = None
    message: str
    booking: Optional[CalcomBooking] = None
    error_code: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


class AvailabilitySlot(BaseModel):
    """Represents an available time slot"""

    time: str = Field(
        ...,
        description="Available time range (e.g., '2025-07-02T11:30:00.000Z - 2025-07-02T12:00:00.000Z')",
    )
    attendees: int = Field(0, description="Number of attendees for this slot")
    booking_url: Optional[str] = Field(
        None, description="Direct booking URL for this slot"
    )


class AvailabilityRequest(BaseModel):
    """Request model for checking availability"""

    event_type_id: int = Field(..., description="Cal.com Event Type ID")
    date_from: str = Field(..., description="Start date in YYYY-MM-DD format")
    date_to: str = Field(..., description="End date in YYYY-MM-DD format")
    timezone: str = Field("UTC", description="Timezone for the availability check")
    username: Optional[str] = Field(None, description="Cal.com username")
    user_id: Optional[int] = Field(None, description="Cal.com user ID")
    team_id: Optional[int] = Field(None, description="Cal.com team ID")


class AvailabilityResponse(BaseModel):
    """Response model for availability checking"""

    success: bool
    event_type_id: int
    date: str = Field(..., description="Date checked in YYYY-MM-DD format")
    available_slots: List[AvailabilitySlot] = []
    timezone: str = Field("UTC", description="Timezone for the slots")
    message: str
    total_slots: int = Field(0, description="Total number of available slots")

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class CalcomBooking(BaseModel):
    """Represents a Cal.com booking"""

    id: int
    title: str
    description: Optional[str] = None
    start_time: datetime
    end_time: datetime
    attendees: List[Dict[str, Any]] = []
    location: Optional[str] = None
    event_type_id: int
    status: str = "confirmed"
    uid: str


class CreateBookingRequest(BaseModel):
    """Simplified request model for creating a Cal.com booking"""

    event_type_id: int = Field(..., description="Cal.com Event Type ID")
    start_time: str = Field(
        ..., description="Start time in ISO format (e.g., '2024-01-15T14:00:00')"
    )
    end_time: str = Field(
        ..., description="End time in ISO format (e.g., '2024-01-15T15:00:00')"
    )
    attendee_name: str = Field(..., description="Attendee full name")
    attendee_email: str = Field(..., description="Attendee email address")


class EventType(BaseModel):
    """Simplified Cal.com Event Type"""

    id: int
    title: str
    slug: Optional[str] = None
    length: int  # in minutes
    description: Optional[str] = None
    locations: List[Dict[str, Any]] = []


class BookingListResponse(BaseModel):
    """Simplified response model for listing bookings"""

    bookings: List[CalcomBooking]
    total_count: int
    message: str


class EventTypeListResponse(BaseModel):
    """Simplified response model for listing event types"""

    event_types: List[EventType]
    total_count: int
    message: str


class CreateBookingResponse(BaseModel):
    """Response model for booking creation"""

    success: bool
    booking_id: Optional[int] = None
    booking_uid: Optional[str] = None
    booking_url: Optional[str] = None
    message: str
    booking: Optional[CalcomBooking] = None
    error_code: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None


class AvailabilitySlot(BaseModel):
    """Simplified available time slot"""

    time: str = Field(
        ...,
        description="Available time in ISO format (e.g., '2024-01-15T14:00:00')",
    )
    attendees: int = Field(0, description="Number of attendees for this slot")
    booking_url: Optional[str] = Field(None, description="Booking URL if available")


class AvailabilityRequest(BaseModel):
    """Simplified request model for checking availability"""

    event_type_id: int = Field(..., description="Cal.com Event Type ID")

    # Support both old and new field names for backward compatibility
    date: Optional[str] = Field(
        None,
        description="Date to check in YYYY-MM-DD format (e.g., '2024-01-15') - legacy field",
    )
    date_from: Optional[str] = Field(
        None,
        description="Start date to check in ISO format (e.g., '2024-01-15' or '2024-01-15T00:00:00Z')",
    )
    date_to: Optional[str] = Field(
        None,
        description="End date to check in ISO format (defaults to date_from if not provided)",
    )
    timezone: Optional[str] = Field(
        "UTC", description="Timezone for the availability check"
    )

    def model_post_init(self, __context) -> None:
        """Post-initialization to handle backward compatibility"""
        # If date is provided but date_from is not, use date as date_from
        if self.date and not self.date_from:
            self.date_from = self.date
        # If date_from is provided but date is not, use date_from as date
        elif self.date_from and not self.date:
            self.date = self.date_from
        # If neither is provided, raise an error
        elif not self.date and not self.date_from:
            raise ValueError("Either 'date' or 'date_from' must be provided")

        # Ensure date_from is always set for internal use
        if not self.date_from:
            self.date_from = self.date


class AvailabilityResponse(BaseModel):
    """Simplified response model for availability checking"""

    success: bool
    event_type_id: int
    date: str = Field(..., description="Date checked in YYYY-MM-DD format")
    available_slots: List[AvailabilitySlot] = []
    timezone: Optional[str] = Field("UTC", description="Timezone used for the check")
    message: str
    total_slots: int = Field(0, description="Total number of available slots")

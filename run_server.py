#!/usr/bin/env python3
"""
Cal.com MCP Server Entry Point
"""

import logging
from src.server import mcp
from src.config import settings
from pydantic_settings import BaseSettings
from pydantic import Field


def main():
    """Main entry point for the Cal.com MCP server"""
    logging.basicConfig(
        level=getattr(logging, settings.log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Starting {settings.mcp_server_name}")
    logger.info(f"Cal.com API Base: {settings.calcom_base_url}")
    logger.info(
        f"Server will run on {settings.mcp_server_host}:{settings.mcp_server_port}"
    )

    # Run the FastMCP server with Streamable HTTP transport
    mcp.run(
        transport="streamable-http",
        host=settings.mcp_server_host,
        port=settings.mcp_server_port,
        path="/mcp",
    )


if __name__ == "__main__":
    main()
